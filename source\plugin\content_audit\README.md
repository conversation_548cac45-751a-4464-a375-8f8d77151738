# Discuz内容审核插件

## 插件简介

本插件是一个基于腾讯云COS的内容审核插件，可以将Discuz论坛的主题和回复内容上传到腾讯云COS进行异步文本审核，帮助论坛管理员自动识别违规内容。

## 主要功能

1. **批量内容审核**: 支持选择多个主题进行批量审核
2. **智能内容提取**: 自动提取主题内容和前10条回复进行审核
3. **异步审核处理**: 支持腾讯云异步审核，提高处理效率
4. **详细审核结果**: 展示审核状态、建议、触发关键词等详细信息
5. **回调处理**: 支持腾讯云审核完成后的回调通知
6. **管理后台集成**: 完全集成到Discuz管理后台

## 安装要求

- Discuz! X3.4 或更高版本
- PHP 5.6 或更高版本
- 腾讯云COS账号和相关权限
- 腾讯云内容审核服务开通

## 安装步骤

1. **上传插件文件**
   - 将整个 `content_audit` 文件夹上传到 `source/plugin/` 目录下
   - 将 `discuz_plugin_content_audit.xml` 上传到论坛根目录

2. **安装插件**
   - 登录Discuz管理后台
   - 进入 "应用" -> "插件" -> "安装新插件"
   - 选择 `discuz_plugin_content_audit.xml` 文件进行安装

3. **配置腾讯云参数**
   - 安装完成后，进入插件管理页面
   - 点击 "配置管理" 填写腾讯云COS相关参数：
     - 地域(Region): 如 `ap-beijing`
     - 存储桶名称: 如 `mybucket-1250000000`
     - SecretId: 腾讯云API密钥ID
     - SecretKey: 腾讯云API密钥Key
     - 审核回调地址(可选): 如 `https://yourdomain.com/source/plugin/content_audit/callback.php`

## 使用说明

### 1. 执行内容审核

1. 进入插件管理页面，点击 "执行内容审核"
2. 选择要审核的主题（支持批量选择）
3. 点击 "开始审核" 提交审核任务
4. 系统将自动提取主题内容和前10条回复进行审核

### 2. 查看审核结果

1. 点击 "查看审核结果" 查看所有审核记录
2. 审核状态包括：
   - **审核中**: 任务已提交，等待腾讯云处理
   - **已完成**: 审核完成，可查看详细结果
   - **失败**: 审核失败，可查看错误信息

3. 审核结果包括：
   - **建议通过**: 内容正常，建议通过
   - **建议人工复审**: 内容可疑，建议人工复审
   - **建议拦截**: 内容违规，建议拦截

### 3. 查看详细结果

点击审核记录的 "详情" 链接可以查看：
- 基本信息（主题信息、审核状态等）
- 审核结果（建议、标签、触发关键词等）
- 原始审核数据

## 数据库表结构

插件会自动创建以下数据表：

```sql
CREATE TABLE `pre_plugin_content_audit` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `thread_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '主题ID',
    `job_id` varchar(100) NOT NULL DEFAULT '' COMMENT '审核任务ID',
    `status` enum('pending','completed','failed') NOT NULL DEFAULT 'pending' COMMENT '审核状态',
    `audit_result` text NOT NULL COMMENT '审核结果JSON',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `thread_id` (`thread_id`),
    KEY `job_id` (`job_id`),
    KEY `status` (`status`),
    KEY `create_time` (`create_time`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='内容审核记录表';
```

## 文件结构

```
source/plugin/content_audit/
├── admincp.inc.php          # 后台管理主文件
├── cos_client.php           # 腾讯云COS客户端
├── install.php              # 安装/卸载脚本
├── callback.php             # 审核回调处理
├── audit_results.php        # 审核结果详情页面
├── language/
│   └── lang_sc_utf8.php     # 简体中文语言包
├── logs/                    # 日志目录（自动创建）
└── README.md                # 说明文档
```

## 注意事项

1. **权限配置**: 确保腾讯云账号有COS和内容审核服务的相关权限
2. **存储桶配置**: 确保COS存储桶已创建并开通内容审核功能
3. **回调地址**: 如果配置了回调地址，确保服务器可以接收腾讯云的回调请求
4. **日志监控**: 定期检查 `logs/` 目录下的日志文件，监控插件运行状态

## 故障排除

### 常见问题

1. **配置错误**: 检查腾讯云参数是否正确填写
2. **权限问题**: 确认腾讯云账号权限和存储桶权限
3. **网络问题**: 检查服务器网络连接和防火墙设置
4. **回调失败**: 检查回调地址是否可访问，查看回调日志

### 日志文件

- 回调日志: `logs/callback_YYYY-MM-DD.log`
- 错误日志: 查看Discuz系统日志

## 技术支持

如有问题，请检查：
1. 插件日志文件
2. Discuz系统日志
3. 腾讯云控制台审核记录
4. 服务器错误日志

## 版本信息

- 版本: 1.0
- 作者: 内容审核系统
- 更新时间: 2025-01-27
- 兼容性: Discuz! X3.4+

## 更新日志

### v1.0 (2025-01-27)
- 初始版本发布
- 支持基本的文本内容审核功能
- 集成腾讯云COS SDK
- 提供完整的管理后台界面
