<?php

/**
 * 内容审核插件 - 安装脚本
 * 
 * <AUTHOR>
 * @version 1.0
 */

if(!defined('IN_DISCUZ')) {
    exit('Access Denied');
}

/**
 * 插件安装
 */
function content_audit_install() {
    global $_G;
    
    // 创建审核记录表
    $sql = "CREATE TABLE IF NOT EXISTS `".DB::table('plugin_content_audit')."` (
        `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
        `thread_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '主题ID',
        `job_id` varchar(100) NOT NULL DEFAULT '' COMMENT '审核任务ID',
        `status` enum('pending','completed','failed') NOT NULL DEFAULT 'pending' COMMENT '审核状态',
        `audit_result` text NOT NULL COMMENT '审核结果JSON',
        `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
        `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
        PRIMARY KEY (`id`),
        KEY `thread_id` (`thread_id`),
        KEY `job_id` (`job_id`),
        KEY `status` (`status`),
        KEY `create_time` (`create_time`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='内容审核记录表'";
    
    DB::query($sql);
    
    // 插入默认配置
    $default_configs = array(
        'cos_region' => '',
        'cos_bucket' => '',
        'cos_secret_id' => '',
        'cos_secret_key' => '',
        'audit_callback_url' => ''
    );
    
    foreach($default_configs as $key => $value) {
        $check_sql = "SELECT COUNT(*) as count FROM ".DB::table('common_setting')." WHERE skey = '$key'";
        $check_result = DB::fetch_first($check_sql);
        
        if($check_result['count'] == 0) {
            $insert_sql = "INSERT INTO ".DB::table('common_setting')." (skey, svalue) VALUES ('$key', '$value')";
            DB::query($insert_sql);
        }
    }
    
    return true;
}

/**
 * 插件卸载
 */
function content_audit_uninstall() {
    global $_G;
    
    // 删除审核记录表
    $sql = "DROP TABLE IF EXISTS `".DB::table('plugin_content_audit')."`";
    DB::query($sql);
    
    // 删除配置项
    $config_keys = array('cos_region', 'cos_bucket', 'cos_secret_id', 'cos_secret_key', 'audit_callback_url');
    $delete_sql = "DELETE FROM ".DB::table('common_setting')." WHERE skey IN ('".implode("','", $config_keys)."')";
    DB::query($delete_sql);
    
    return true;
}

/**
 * 插件升级
 */
function content_audit_upgrade() {
    // 暂时不需要升级逻辑
    return true;
}

// 根据操作执行相应函数
if(isset($_GET['operation'])) {
    switch($_GET['operation']) {
        case 'install':
            if(content_audit_install()) {
                echo "插件安装成功！";
            } else {
                echo "插件安装失败！";
            }
            break;
        case 'uninstall':
            if(content_audit_uninstall()) {
                echo "插件卸载成功！";
            } else {
                echo "插件卸载失败！";
            }
            break;
        case 'upgrade':
            if(content_audit_upgrade()) {
                echo "插件升级成功！";
            } else {
                echo "插件升级失败！";
            }
            break;
    }
}

?>
