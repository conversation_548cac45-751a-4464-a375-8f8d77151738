<?php

/**
 * 内容审核插件 - 安装脚本
 *
 * <AUTHOR>
 * @version 1.0
 */

if(!defined('IN_DISCUZ')) {
    exit('Access Denied');
}

// 创建审核记录表
$sql = "CREATE TABLE IF NOT EXISTS `".DB::table('plugin_content_audit')."` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `thread_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '主题ID',
    `job_id` varchar(100) NOT NULL DEFAULT '' COMMENT '审核任务ID',
    `status` enum('pending','completed','failed') NOT NULL DEFAULT 'pending' COMMENT '审核状态',
    `audit_result` text NOT NULL COMMENT '审核结果JSON',
    `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `thread_id` (`thread_id`),
    KEY `job_id` (`job_id`),
    KEY `status` (`status`),
    KEY `create_time` (`create_time`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='内容审核记录表'";

runquery($sql);

$finish = TRUE;

?>
