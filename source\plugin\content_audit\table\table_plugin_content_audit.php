<?php

if(!defined('IN_DISCUZ')) {
    exit('Access Denied');
}

class table_plugin_content_audit extends discuz_table {
    
    public function __construct() {
        $this->_table = 'salary_check';
        $this->_pk    = 'id';
        
        parent::__construct();
    }

    public function add_by_uid($uid, $date, $manageCount, $postsCount, $leaveDays) {
        return DB::insert($this->_table, array(
            'uid' => $uid,
            'date' => $date,
            'manageCount' => $manageCount,
            'postsCount' => $postsCount,
            'leaveDays' => $leaveDays
        ), true);
    }

    public function fetch_all_by_uid_date_range($uid, $start_date, $end_date) {
        return DB::fetch_all("SELECT * FROM %t WHERE uid=%d AND date>=%d AND date<=%d",
            array($this->_table, $uid, $start_date, $end_date));
    }
    
    public function fetch_all_by_date_range($start_date, $end_date) {
        return DB::fetch_all("SELECT * FROM %t WHERE date>=%d AND date<=%d ORDER BY date DESC",
            array($this->_table, $start_date, $end_date));
    }
}

?>

