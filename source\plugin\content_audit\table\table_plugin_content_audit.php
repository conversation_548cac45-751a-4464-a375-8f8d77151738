<?php

if(!defined('IN_DISCUZ')) {
    exit('Access Denied');
}

class table_plugin_content_audit extends discuz_table {

    public function __construct() {
        $this->_table = 'plugin_content_audit';
        $this->_pk    = 'id';

        parent::__construct();
    }

    /**
     * 根据主题ID获取审核记录
     *
     * @param int $thread_id 主题ID
     * @return array 审核记录列表
     */
    public function fetch_by_thread_id($thread_id) {
        return $this->fetch_all('thread_id', intval($thread_id), null, null, 'create_time DESC');
    }

    /**
     * 根据任务ID获取审核记录
     *
     * @param string $job_id 任务ID
     * @return array|false 审核记录
     */
    public function fetch_by_job_id($job_id) {
        return $this->fetch($job_id, 'job_id');
    }

    /**
     * 获取指定状态的审核记录
     *
     * @param string $status 状态：pending, completed, failed
     * @param int $limit 限制数量
     * @return array 审核记录列表
     */
    public function fetch_by_status($status, $limit = 100) {
        return $this->fetch_all('status', $status, null, $limit, 'create_time DESC');
    }

    /**
     * 获取最新的审核记录
     *
     * @param int $limit 限制数量
     * @return array 审核记录列表
     */
    public function fetch_latest($limit = 100) {
        return DB::fetch_all("SELECT * FROM %t ORDER BY create_time DESC LIMIT %d",
                            array($this->_table, intval($limit)));
    }

    /**
     * 插入审核记录
     *
     * @param array $data 审核数据
     * @return int 插入的记录ID
     */
    public function insert_audit_record($data) {
        $insert_data = array(
            'thread_id' => intval($data['thread_id']),
            'job_id' => $data['job_id'],
            'status' => $data['status'],
            'audit_result' => $data['audit_result'],
            'create_time' => isset($data['create_time']) ? intval($data['create_time']) : TIMESTAMP,
            'update_time' => isset($data['update_time']) ? intval($data['update_time']) : TIMESTAMP
        );

        return $this->insert($insert_data, true);
    }

    /**
     * 更新审核记录
     *
     * @param int $id 记录ID
     * @param array $data 更新数据
     * @return bool 更新结果
     */
    public function update_audit_record($id, $data) {
        $update_data = array();

        if(isset($data['status'])) {
            $update_data['status'] = $data['status'];
        }

        if(isset($data['audit_result'])) {
            $update_data['audit_result'] = $data['audit_result'];
        }

        $update_data['update_time'] = TIMESTAMP;

        return $this->update(intval($id), $update_data);
    }

    /**
     * 根据任务ID更新审核记录
     *
     * @param string $job_id 任务ID
     * @param array $data 更新数据
     * @return bool 更新结果
     */
    public function update_by_job_id($job_id, $data) {
        $update_data = array();

        if(isset($data['status'])) {
            $update_data['status'] = $data['status'];
        }

        if(isset($data['audit_result'])) {
            $update_data['audit_result'] = $data['audit_result'];
        }

        $update_data['update_time'] = TIMESTAMP;

        $set_sql = array();
        foreach($update_data as $key => $value) {
            $set_sql[] = "`{$key}`='".addslashes($value)."'";
        }

        return DB::query("UPDATE %t SET ".implode(',', $set_sql)." WHERE job_id=%s",
                        array($this->_table, $job_id));
    }

    /**
     * 获取审核统计信息
     *
     * @return array 统计信息
     */
    public function get_statistics() {
        $stats = array(
            'total' => 0,
            'pending' => 0,
            'completed' => 0,
            'failed' => 0
        );

        $result = DB::fetch_all("SELECT status, COUNT(*) as count FROM %t GROUP BY status",
                               array($this->_table));

        foreach($result as $row) {
            $stats[$row['status']] = intval($row['count']);
            $stats['total'] += intval($row['count']);
        }

        return $stats;
    }

    /**
     * 检查主题是否已经审核过
     *
     * @param int $thread_id 主题ID
     * @return bool 是否已审核
     */
    public function is_thread_audited($thread_id) {
        $count = $this->count('thread_id', intval($thread_id));
        return $count > 0;
    }

    /**
     * 获取待处理的审核记录（用于定时查询结果）
     *
     * @param int $limit 限制数量
     * @return array 待处理记录列表
     */
    public function fetch_pending_jobs($limit = 50) {
        return DB::fetch_all("SELECT * FROM %t WHERE status='pending' AND job_id!='' ORDER BY create_time ASC LIMIT %d",
                            array($this->_table, intval($limit)));
    }

}

?>

