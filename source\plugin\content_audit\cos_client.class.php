<?php

/**
 * 腾讯云COS客户端类
 * 
 * <AUTHOR>
 * @version 1.0
 */

if(!defined('IN_DISCUZ')) {
    exit('Access Denied');
}

// 引入腾讯云COS SDK
require_once dirname(dirname(dirname(dirname(__FILE__)))) . '/cos-sdk-v5-7.phar';

use Qcloud\Cos\Client;

class ContentAuditCOSClient {

    private $cosClient;
    private $config;
    private $auditTable;

    public function __construct() {
        $this->loadConfig();
        $this->initCOSClient();
        $this->auditTable = C::t('#content_audit#plugin_content_audit');
    }
    
    /**
     * 加载配置
     */
    private function loadConfig() {
        global $_G;

        // 加载插件配置缓存
        loadcache('plugin');
        $plugin_config = $_G['cache']['plugin']['content_audit'] ?? array();

        $this->config = array(
            'cos_region' => $plugin_config['cos_region'] ?? '',
            'cos_bucket' => $plugin_config['cos_bucket'] ?? '',
            'cos_secret_id' => $plugin_config['cos_secret_id'] ?? '',
            'cos_secret_key' => $plugin_config['cos_secret_key'] ?? '',
            'audit_callback_url' => $plugin_config['audit_callback_url'] ?? ''
        );
    }
    
    /**
     * 初始化COS客户端
     */
    private function initCOSClient() {
        if(empty($this->config['cos_secret_id']) || empty($this->config['cos_secret_key']) || empty($this->config['cos_region'])) {
            throw new Exception('腾讯云COS配置不完整，请先在插件设置中配置相关参数');
        }
        
        $this->cosClient = new Client(array(
            'region' => $this->config['cos_region'],
            'schema' => 'https',
            'credentials' => array(
                'secretId' => $this->config['cos_secret_id'],
                'secretKey' => $this->config['cos_secret_key']
            )
        ));
    }
    
    /**
     * 审核主题内容
     * 
     * @param int $thread_id 主题ID
     * @return array 审核结果
     */
    public function auditThreadContent($thread_id) {
        try {
            // 获取主题信息
            $thread = $this->getThreadInfo($thread_id);
            if(!$thread) {
                throw new Exception('主题不存在');
            }
            
            // 获取主题内容和前10条回复
            $content = $this->getThreadContent($thread_id);
            
            // 上传内容到COS并提交审核
            $audit_result = $this->submitTextAudit($thread_id, $content);
            
            // 保存审核记录
            $this->saveAuditRecord($thread_id, $audit_result);
            
            return $audit_result;
            
        } catch(Exception $e) {
            // 记录错误
            $this->saveAuditRecord($thread_id, array(
                'status' => 'failed',
                'error' => $e->getMessage()
            ));
            
            throw $e;
        }
    }
    
    /**
     * 获取主题信息
     * 
     * @param int $thread_id 主题ID
     * @return array|false 主题信息
     */
    private function getThreadInfo($thread_id) {
        $query = DB::query("SELECT * FROM ".DB::table('forum_thread')." WHERE tid = ".intval($thread_id));
        return DB::fetch($query);
    }
    
    /**
     * 获取主题内容和回复
     * 
     * @param int $thread_id 主题ID
     * @return string 合并后的内容
     */
    private function getThreadContent($thread_id) {
        $content_parts = array();
        
        // 获取主题内容
        $query = DB::query("SELECT subject, message FROM ".DB::table('forum_post')." WHERE tid = ".intval($thread_id)." AND first = 1");
        $thread_post = DB::fetch($query);
        
        if($thread_post) {
            $content_parts[] = "主题标题: " . $thread_post['subject'];
            $content_parts[] = "主题内容: " . strip_tags($thread_post['message']);
        }
        
        // 获取前10条回复
        $query = DB::query("SELECT message, author, dateline FROM ".DB::table('forum_post')." 
                           WHERE tid = ".intval($thread_id)." AND first = 0 
                           ORDER BY dateline ASC 
                           LIMIT 10");
        
        $reply_count = 0;
        while($reply = DB::fetch($query)) {
            $reply_count++;
            $content_parts[] = "回复{$reply_count}({$reply['author']}): " . strip_tags($reply['message']);
        }
        
        return implode("\n\n", $content_parts);
    }
    
    /**
     * 提交文本审核
     * 
     * @param int $thread_id 主题ID
     * @param string $content 要审核的内容
     * @return array 审核结果
     */
    private function submitTextAudit($thread_id, $content) {
        try {
            // 将内容进行base64编码
            $content_base64 = base64_encode($content);
            
            // 构建审核请求参数
            $params = array(
                'Bucket' => $this->config['cos_bucket'],
                'Input' => array(
                    'Content' => $content_base64
                ),
                'Conf' => array()
            );
            
            // 如果配置了回调地址，添加到请求中
            if(!empty($this->config['audit_callback_url'])) {
                $params['Conf']['Callback'] = $this->config['audit_callback_url'];
            }
            
            // 调用腾讯云文本审核API
            $result = $this->cosClient->createAuditingTextJobs($params);
            
            // 处理审核结果
            if($result['JobsDetail']['State'] == 'Success') {
                // 同步审核，直接返回结果
                return array(
                    'status' => 'completed',
                    'job_id' => $result['JobsDetail']['JobId'],
                    'suggestion' => $result['JobsDetail']['Suggestion'],
                    'label' => $result['JobsDetail']['Label'],
                    'result' => $result['JobsDetail']['Result'],
                    'keywords' => $this->extractKeywords($result['JobsDetail'])
                );
            } else {
                // 异步审核，返回任务ID
                return array(
                    'status' => 'pending',
                    'job_id' => $result['JobsDetail']['JobId']
                );
            }
            
        } catch(Exception $e) {
            throw new Exception('提交审核失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 提取关键词
     * 
     * @param array $job_detail 审核详情
     * @return array 关键词列表
     */
    private function extractKeywords($job_detail) {
        $keywords = array();
        
        if(isset($job_detail['SectionCount']) && $job_detail['SectionCount'] > 0) {
            foreach($job_detail['Section'] as $section) {
                if(isset($section['PornInfo']['Keywords'])) {
                    $keywords = array_merge($keywords, explode(',', $section['PornInfo']['Keywords']));
                }
                if(isset($section['TerrorismInfo']['Keywords'])) {
                    $keywords = array_merge($keywords, explode(',', $section['TerrorismInfo']['Keywords']));
                }
                if(isset($section['PoliticsInfo']['Keywords'])) {
                    $keywords = array_merge($keywords, explode(',', $section['PoliticsInfo']['Keywords']));
                }
                if(isset($section['AdsInfo']['Keywords'])) {
                    $keywords = array_merge($keywords, explode(',', $section['AdsInfo']['Keywords']));
                }
                if(isset($section['IllegalInfo']['Keywords'])) {
                    $keywords = array_merge($keywords, explode(',', $section['IllegalInfo']['Keywords']));
                }
                if(isset($section['AbuseInfo']['Keywords'])) {
                    $keywords = array_merge($keywords, explode(',', $section['AbuseInfo']['Keywords']));
                }
            }
        }
        
        return array_unique(array_filter($keywords));
    }
    
    /**
     * 保存审核记录
     *
     * @param int $thread_id 主题ID
     * @param array $audit_result 审核结果
     */
    private function saveAuditRecord($thread_id, $audit_result) {
        $status = isset($audit_result['status']) ? $audit_result['status'] : 'failed';
        $job_id = isset($audit_result['job_id']) ? $audit_result['job_id'] : '';
        $result_json = json_encode($audit_result);

        // 检查是否已存在记录
        $existing = $this->auditTable->fetch_by_job_id($job_id);

        if($existing && $existing['thread_id'] == $thread_id) {
            // 更新现有记录
            $this->auditTable->update_audit_record($existing['id'], array(
                'status' => $status,
                'audit_result' => $result_json
            ));
        } else {
            // 插入新记录
            $this->auditTable->insert_audit_record(array(
                'thread_id' => $thread_id,
                'job_id' => $job_id,
                'status' => $status,
                'audit_result' => $result_json
            ));
        }
    }
    
    /**
     * 查询审核结果
     * 
     * @param string $job_id 任务ID
     * @return array 审核结果
     */
    public function queryAuditResult($job_id) {
        try {
            $params = array(
                'Bucket' => $this->config['cos_bucket'],
                'JobId' => $job_id
            );
            
            $result = $this->cosClient->describeAuditingTextJob($params);
            
            if($result['JobsDetail']['State'] == 'Success') {
                return array(
                    'status' => 'completed',
                    'suggestion' => $result['JobsDetail']['Suggestion'],
                    'label' => $result['JobsDetail']['Label'],
                    'result' => $result['JobsDetail']['Result'],
                    'keywords' => $this->extractKeywords($result['JobsDetail'])
                );
            } else {
                return array(
                    'status' => 'pending'
                );
            }
            
        } catch(Exception $e) {
            return array(
                'status' => 'failed',
                'error' => $e->getMessage()
            );
        }
    }
}

?>
