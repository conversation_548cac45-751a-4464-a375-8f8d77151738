<?php

/**
 * 内容审核插件 - 审核结果详情页面
 * 
 * <AUTHOR>
 * @version 1.0
 */

if(!defined('IN_DISCUZ') || !defined('IN_ADMINCP')) {
    exit('Access Denied');
}

require_once dirname(__FILE__) . '/cos_client.php';

// 获取审核记录ID
$audit_id = intval($_GET['id']);

if($audit_id <= 0) {
    cpmsg('无效的审核记录ID！', '', 'error');
}

// 获取审核记录详情
$query = DB::query("SELECT * FROM ".DB::table('plugin_content_audit')." WHERE id = $audit_id");
$audit_record = DB::fetch($query);

if(!$audit_record) {
    cpmsg('审核记录不存在！', '', 'error');
}

// 获取主题信息
$query = DB::query("SELECT t.*, f.name as forum_name FROM ".DB::table('forum_thread')." t 
                   LEFT JOIN ".DB::table('forum_forum')." f ON t.fid = f.fid 
                   WHERE t.tid = ".$audit_record['thread_id']);
$thread_info = DB::fetch($query);

// 解析审核结果
$audit_result = json_decode($audit_record['audit_result'], true);

// 如果是待审核状态，尝试查询最新结果
if($audit_record['status'] == 'pending' && !empty($audit_record['job_id'])) {
    try {
        $cos_client = new ContentAuditCOSClient();
        $latest_result = $cos_client->queryAuditResult($audit_record['job_id']);
        
        if($latest_result['status'] != 'pending') {
            // 更新数据库记录
            $update_sql = "UPDATE ".DB::table('plugin_content_audit')." 
                          SET status = '".addslashes($latest_result['status'])."', 
                              audit_result = '".addslashes(json_encode($latest_result))."',
                              update_time = ".time()."
                          WHERE id = $audit_id";
            DB::query($update_sql);
            
            // 更新当前记录
            $audit_record['status'] = $latest_result['status'];
            $audit_record['audit_result'] = json_encode($latest_result);
            $audit_result = $latest_result;
        }
    } catch(Exception $e) {
        // 查询失败，保持原状态
    }
}

?>

<div class="main">
    <div class="content">
        <h3>审核结果详情</h3>
        
        <!-- 基本信息 -->
        <div class="contentbox">
            <h4>基本信息</h4>
            <table class="tb tb2">
                <tr>
                    <td class="td1" width="120">审核记录ID:</td>
                    <td class="td2"><?php echo $audit_record['id']; ?></td>
                </tr>
                <tr>
                    <td class="td1">主题ID:</td>
                    <td class="td2"><?php echo $audit_record['thread_id']; ?></td>
                </tr>
                <?php if($thread_info): ?>
                <tr>
                    <td class="td1">主题标题:</td>
                    <td class="td2"><?php echo htmlspecialchars($thread_info['subject']); ?></td>
                </tr>
                <tr>
                    <td class="td1">主题作者:</td>
                    <td class="td2"><?php echo htmlspecialchars($thread_info['author']); ?></td>
                </tr>
                <tr>
                    <td class="td1">所属版块:</td>
                    <td class="td2"><?php echo htmlspecialchars($thread_info['forum_name']); ?></td>
                </tr>
                <tr>
                    <td class="td1">发布时间:</td>
                    <td class="td2"><?php echo date('Y-m-d H:i:s', $thread_info['dateline']); ?></td>
                </tr>
                <?php endif; ?>
                <tr>
                    <td class="td1">任务ID:</td>
                    <td class="td2"><?php echo htmlspecialchars($audit_record['job_id']); ?></td>
                </tr>
                <tr>
                    <td class="td1">审核状态:</td>
                    <td class="td2">
                        <?php
                        switch($audit_record['status']) {
                            case 'pending':
                                echo '<span style="color: orange;">审核中</span>';
                                break;
                            case 'completed':
                                echo '<span style="color: green;">已完成</span>';
                                break;
                            case 'failed':
                                echo '<span style="color: red;">失败</span>';
                                break;
                        }
                        ?>
                    </td>
                </tr>
                <tr>
                    <td class="td1">提交时间:</td>
                    <td class="td2"><?php echo date('Y-m-d H:i:s', $audit_record['create_time']); ?></td>
                </tr>
                <tr>
                    <td class="td1">更新时间:</td>
                    <td class="td2"><?php echo date('Y-m-d H:i:s', $audit_record['update_time']); ?></td>
                </tr>
            </table>
        </div>
        
        <!-- 审核结果 -->
        <?php if($audit_record['status'] == 'completed' && $audit_result): ?>
        <div class="contentbox">
            <h4>审核结果</h4>
            <table class="tb tb2">
                <tr>
                    <td class="td1" width="120">审核建议:</td>
                    <td class="td2">
                        <?php
                        $suggestion = isset($audit_result['suggestion']) ? $audit_result['suggestion'] : '';
                        switch($suggestion) {
                            case 'Pass':
                                echo '<span style="color: green; font-weight: bold;">建议通过</span>';
                                break;
                            case 'Review':
                                echo '<span style="color: orange; font-weight: bold;">建议人工复审</span>';
                                break;
                            case 'Block':
                                echo '<span style="color: red; font-weight: bold;">建议拦截</span>';
                                break;
                            default:
                                echo htmlspecialchars($suggestion);
                        }
                        ?>
                    </td>
                </tr>
                <tr>
                    <td class="td1">审核标签:</td>
                    <td class="td2"><?php echo htmlspecialchars(isset($audit_result['label']) ? $audit_result['label'] : ''); ?></td>
                </tr>
                <tr>
                    <td class="td1">审核结果:</td>
                    <td class="td2"><?php echo htmlspecialchars(isset($audit_result['result']) ? $audit_result['result'] : ''); ?></td>
                </tr>
                <?php if(isset($audit_result['keywords']) && !empty($audit_result['keywords'])): ?>
                <tr>
                    <td class="td1">触发关键词:</td>
                    <td class="td2">
                        <?php
                        $keywords = is_array($audit_result['keywords']) ? $audit_result['keywords'] : array();
                        foreach($keywords as $keyword) {
                            echo '<span style="background: #ffcccc; padding: 2px 5px; margin: 2px; border-radius: 3px;">'.htmlspecialchars($keyword).'</span> ';
                        }
                        ?>
                    </td>
                </tr>
                <?php endif; ?>
            </table>
        </div>
        <?php endif; ?>
        
        <!-- 错误信息 -->
        <?php if($audit_record['status'] == 'failed' && isset($audit_result['error'])): ?>
        <div class="contentbox">
            <h4>错误信息</h4>
            <div style="color: red; padding: 10px; background: #fff0f0; border: 1px solid #ffcccc;">
                <?php echo htmlspecialchars($audit_result['error']); ?>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- 原始数据 -->
        <div class="contentbox">
            <h4>原始审核数据</h4>
            <textarea style="width: 100%; height: 200px; font-family: monospace;" readonly><?php echo htmlspecialchars($audit_record['audit_result']); ?></textarea>
        </div>
        
        <!-- 操作按钮 -->
        <div class="fixsel">
            <input type="button" value="返回列表" onclick="location.href='?action=plugins&operation=config&do=<?php echo $_GET['do']; ?>&identifier=content_audit&pmod=admincp&op=results'" class="btn" />
            <?php if($audit_record['status'] == 'pending'): ?>
            <input type="button" value="刷新状态" onclick="location.reload()" class="btn" />
            <?php endif; ?>
            <?php if($thread_info): ?>
            <input type="button" value="查看主题" onclick="window.open('forum.php?mod=viewthread&tid=<?php echo $thread_info['tid']; ?>')" class="btn" />
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.contentbox {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #ddd;
    background: #f9f9f9;
}

.contentbox h4 {
    margin: 0 0 10px 0;
    padding: 0;
    color: #333;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
}

.tb.tb2 td.td1 {
    background: #f0f0f0;
    font-weight: bold;
}

.tb.tb2 td.td2 {
    background: #fff;
}
</style>
