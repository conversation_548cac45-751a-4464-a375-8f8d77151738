<?php

/**
 * 内容审核插件 - 后台管理页面
 * 
 * <AUTHOR>
 * @version 1.0
 */

if(!defined('IN_DISCUZ') || !defined('IN_ADMINCP')) {
    exit('Access Denied');
}

// 加载插件配置缓存
loadcache('plugin');
$plugin_config = $_G['cache']['plugin']['content_audit'] ?? array();

// 检查管理员权限
if(!$_G['adminid']) {
    cpmsg('您没有权限访问此页面！', '', 'error');
}

require_once dirname(__FILE__) . '/cos_client.class.php';

// 获取操作类型
$operation = $_GET['operation'] ? $_GET['operation'] : $_POST['operation'];

// 处理表单提交
if($_POST['submit']) {
    if($operation == 'audit') {
        // 执行审核
        $thread_ids = $_POST['thread_ids'];
        if(empty($thread_ids)) {
            cpmsg('请选择要审核的主题！', '', 'error');
        }
        
        $cos_client = new ContentAuditCOSClient();
        $audit_results = array();
        
        foreach(explode(',', $thread_ids) as $tid) {
            $tid = intval($tid);
            if($tid > 0) {
                try {
                    $result = $cos_client->auditThreadContent($tid);
                    $audit_results[] = $result;
                } catch(Exception $e) {
                    // 记录错误但继续处理其他主题
                    continue;
                }
            }
        }
        
        cpmsg('审核任务已提交，共提交 '.count($audit_results).' 个任务！', 'action=plugins&operation=config&do='.$pluginid.'&identifier=content_audit&pmod=content_audit&op=results', 'succeed');
    }
}

// 根据操作显示不同页面
$op = $_GET['op'] ? $_GET['op'] : 'main';
switch($op) {
    case 'audit':
        showAuditPage();
        break;
    case 'results':
        showResultsPage();
        break;
    case 'detail':
        showDetailPage();
        break;
    default:
        showMainPage();
        break;
}

/**
 * 显示主页面
 */
function showMainPage() {
    global $lang, $pluginid;
    
    echo '<div class="main">';
    echo '<div class="content">';
    echo '<h3>内容审核插件</h3>';
    echo '<div class="contentbox">';
    echo '<p>欢迎使用内容审核插件！本插件可以将论坛主题和回复内容上传到腾讯云COS进行异步审核。</p>';
    echo '<ul>';
    echo '<li><a href="?action=plugins&operation=config&do='.$pluginid.'&identifier=content_audit&pmod=content_audit&op=audit">执行内容审核</a></li>';
    echo '<li><a href="?action=plugins&operation=config&do='.$pluginid.'&identifier=content_audit&pmod=content_audit&op=results">查看审核结果</a></li>';
    echo '</ul>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
}

/**
 * 显示审核页面
 */
function showAuditPage() {
    global $lang, $pluginid;
    
    // 获取最新的主题列表
    $threads = array();
    $query = DB::query("SELECT t.tid, t.subject, t.author, t.dateline, f.name as forum_name 
                       FROM ".DB::table('forum_thread')." t 
                       LEFT JOIN ".DB::table('forum_forum')." f ON t.fid = f.fid 
                       WHERE t.displayorder >= 0 
                       ORDER BY t.dateline DESC 
                       LIMIT 50");
    
    while($row = DB::fetch($query)) {
        $threads[] = $row;
    }
    
    echo '<div class="main">';
    echo '<div class="content">';
    echo '<h3>执行内容审核</h3>';
    echo '<form method="post" action="">';
    echo '<input type="hidden" name="operation" value="audit" />';
    echo '<div class="contentbox">';
    echo '<p>选择要审核的主题（将审核主题内容及前10条回复）：</p>';
    echo '<table class="tb tb2">';
    echo '<tr><th width="30">选择</th><th>主题标题</th><th>作者</th><th>版块</th><th>发布时间</th></tr>';
    
    foreach($threads as $thread) {
        echo '<tr>';
        echo '<td><input type="checkbox" name="thread_check[]" value="'.$thread['tid'].'" /></td>';
        echo '<td>'.htmlspecialchars($thread['subject']).'</td>';
        echo '<td>'.htmlspecialchars($thread['author']).'</td>';
        echo '<td>'.htmlspecialchars($thread['forum_name']).'</td>';
        echo '<td>'.date('Y-m-d H:i:s', $thread['dateline']).'</td>';
        echo '</tr>';
    }
    
    echo '</table>';
    echo '<div class="fixsel">';
    echo '<input type="button" value="全选" onclick="checkAll()" class="btn" />';
    echo '<input type="button" value="反选" onclick="checkReverse()" class="btn" />';
    echo '<input type="submit" name="submit" value="开始审核" class="btn" onclick="return getSelectedThreads()" />';
    echo '</div>';
    echo '<input type="hidden" name="thread_ids" id="thread_ids" value="" />';
    echo '</div>';
    echo '</form>';
    echo '</div>';
    echo '</div>';
    
    // JavaScript代码
    echo '<script type="text/javascript">';
    echo 'function checkAll() {';
    echo '    var checkboxes = document.getElementsByName("thread_check[]");';
    echo '    for(var i = 0; i < checkboxes.length; i++) {';
    echo '        checkboxes[i].checked = true;';
    echo '    }';
    echo '}';
    echo 'function checkReverse() {';
    echo '    var checkboxes = document.getElementsByName("thread_check[]");';
    echo '    for(var i = 0; i < checkboxes.length; i++) {';
    echo '        checkboxes[i].checked = !checkboxes[i].checked;';
    echo '    }';
    echo '}';
    echo 'function getSelectedThreads() {';
    echo '    var checkboxes = document.getElementsByName("thread_check[]");';
    echo '    var selected = [];';
    echo '    for(var i = 0; i < checkboxes.length; i++) {';
    echo '        if(checkboxes[i].checked) {';
    echo '            selected.push(checkboxes[i].value);';
    echo '        }';
    echo '    }';
    echo '    if(selected.length == 0) {';
    echo '        alert("请选择要审核的主题！");';
    echo '        return false;';
    echo '    }';
    echo '    document.getElementById("thread_ids").value = selected.join(",");';
    echo '    return true;';
    echo '}';
    echo '</script>';
}

/**
 * 显示审核结果页面
 */
function showResultsPage() {
    global $lang, $pluginid;

    // 获取审核结果
    $auditTable = C::t('#content_audit#plugin_content_audit');
    $results = $auditTable->fetch_latest(100);
    
    echo '<div class="main">';
    echo '<div class="content">';
    echo '<h3>审核结果</h3>';
    echo '<div class="contentbox">';
    
    if(empty($results)) {
        echo '<p>暂无审核结果</p>';
    } else {
        echo '<table class="tb tb2">';
        echo '<tr><th>主题ID</th><th>审核状态</th><th>审核结果</th><th>触发关键词</th><th>提交时间</th><th>操作</th></tr>';
        
        foreach($results as $result) {
            $audit_result = json_decode($result['audit_result'], true);
            $status_text = '';
            $result_text = '';
            $keywords = '';
            
            if($result['status'] == 'pending') {
                $status_text = '<span style="color: orange;">审核中</span>';
            } elseif($result['status'] == 'completed') {
                $status_text = '<span style="color: green;">已完成</span>';
                if($audit_result) {
                    $result_text = $audit_result['suggestion'] == 'Pass' ? '<span style="color: green;">通过</span>' : '<span style="color: red;">不通过</span>';
                    if(isset($audit_result['keywords'])) {
                        $keywords = implode(', ', $audit_result['keywords']);
                    }
                }
            } elseif($result['status'] == 'failed') {
                $status_text = '<span style="color: red;">失败</span>';
            }
            
            echo '<tr>';
            echo '<td>'.$result['thread_id'].'</td>';
            echo '<td>'.$status_text.'</td>';
            echo '<td>'.$result_text.'</td>';
            echo '<td>'.htmlspecialchars($keywords).'</td>';
            echo '<td>'.date('Y-m-d H:i:s', $result['create_time']).'</td>';
            echo '<td><a href="?action=plugins&operation=config&do='.$pluginid.'&identifier=content_audit&pmod=content_audit&op=detail&id='.$result['id'].'">详情</a></td>';
            echo '</tr>';
        }
        
        echo '</table>';
    }
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
}

/**
 * 显示详情页面
 */
function showDetailPage() {
    global $lang, $pluginid;

    $audit_id = intval($_GET['id']);

    if($audit_id <= 0) {
        cpmsg('无效的审核记录ID！', '', 'error');
    }

    // 获取审核记录详情
    $auditTable = C::t('#content_audit#plugin_content_audit');
    $audit_record = $auditTable->fetch($audit_id);

    if(!$audit_record) {
        cpmsg('审核记录不存在！', '', 'error');
    }
    
    // 获取主题信息
    $query = DB::query("SELECT t.*, f.name as forum_name FROM ".DB::table('forum_thread')." t 
                       LEFT JOIN ".DB::table('forum_forum')." f ON t.fid = f.fid 
                       WHERE t.tid = ".$audit_record['thread_id']);
    $thread_info = DB::fetch($query);
    
    // 解析审核结果
    $audit_result = json_decode($audit_record['audit_result'], true);
    
    echo '<div class="main">';
    echo '<div class="content">';
    echo '<h3>审核结果详情</h3>';
    
    // 基本信息
    echo '<div class="contentbox">';
    echo '<h4>基本信息</h4>';
    echo '<table class="tb tb2">';
    echo '<tr><td class="td1" width="120">审核记录ID:</td><td class="td2">'.$audit_record['id'].'</td></tr>';
    echo '<tr><td class="td1">主题ID:</td><td class="td2">'.$audit_record['thread_id'].'</td></tr>';
    if($thread_info) {
        echo '<tr><td class="td1">主题标题:</td><td class="td2">'.htmlspecialchars($thread_info['subject']).'</td></tr>';
        echo '<tr><td class="td1">主题作者:</td><td class="td2">'.htmlspecialchars($thread_info['author']).'</td></tr>';
        echo '<tr><td class="td1">所属版块:</td><td class="td2">'.htmlspecialchars($thread_info['forum_name']).'</td></tr>';
    }
    echo '<tr><td class="td1">任务ID:</td><td class="td2">'.htmlspecialchars($audit_record['job_id']).'</td></tr>';
    echo '<tr><td class="td1">审核状态:</td><td class="td2">';
    switch($audit_record['status']) {
        case 'pending':
            echo '<span style="color: orange;">审核中</span>';
            break;
        case 'completed':
            echo '<span style="color: green;">已完成</span>';
            break;
        case 'failed':
            echo '<span style="color: red;">失败</span>';
            break;
    }
    echo '</td></tr>';
    echo '<tr><td class="td1">提交时间:</td><td class="td2">'.date('Y-m-d H:i:s', $audit_record['create_time']).'</td></tr>';
    echo '</table>';
    echo '</div>';
    
    // 审核结果
    if($audit_record['status'] == 'completed' && $audit_result) {
        echo '<div class="contentbox">';
        echo '<h4>审核结果</h4>';
        echo '<table class="tb tb2">';
        echo '<tr><td class="td1" width="120">审核建议:</td><td class="td2">';
        $suggestion = isset($audit_result['suggestion']) ? $audit_result['suggestion'] : '';
        switch($suggestion) {
            case 'Pass':
                echo '<span style="color: green; font-weight: bold;">建议通过</span>';
                break;
            case 'Review':
                echo '<span style="color: orange; font-weight: bold;">建议人工复审</span>';
                break;
            case 'Block':
                echo '<span style="color: red; font-weight: bold;">建议拦截</span>';
                break;
            default:
                echo htmlspecialchars($suggestion);
        }
        echo '</td></tr>';
        echo '<tr><td class="td1">审核标签:</td><td class="td2">'.htmlspecialchars(isset($audit_result['label']) ? $audit_result['label'] : '').'</td></tr>';
        if(isset($audit_result['keywords']) && !empty($audit_result['keywords'])) {
            echo '<tr><td class="td1">触发关键词:</td><td class="td2">';
            $keywords = is_array($audit_result['keywords']) ? $audit_result['keywords'] : array();
            foreach($keywords as $keyword) {
                echo '<span style="background: #ffcccc; padding: 2px 5px; margin: 2px; border-radius: 3px;">'.htmlspecialchars($keyword).'</span> ';
            }
            echo '</td></tr>';
        }
        echo '</table>';
        echo '</div>';
    }
    
    // 操作按钮
    echo '<div class="fixsel">';
    echo '<input type="button" value="返回列表" onclick="location.href=\'?action=plugins&operation=config&do='.$pluginid.'&identifier=content_audit&pmod=content_audit&op=results\'" class="btn" />';
    if($thread_info) {
        echo '<input type="button" value="查看主题" onclick="window.open(\'forum.php?mod=viewthread&tid='.$thread_info['tid'].'\')" class="btn" />';
    }
    echo '</div>';
    
    echo '</div>';
    echo '</div>';
}

?>
