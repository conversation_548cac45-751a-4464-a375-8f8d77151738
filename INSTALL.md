# Discuz内容审核插件安装指南

## 快速安装步骤

### 1. 准备工作

确保您已经：
- 拥有Discuz! X3.4或更高版本的论坛
- 开通了腾讯云COS服务
- 开通了腾讯云内容审核服务
- 获取了腾讯云API密钥（SecretId和SecretKey）

### 2. 上传文件

1. 将 `cos-sdk-v5-7.phar` 文件上传到论坛根目录
2. 将 `source` 文件夹上传到论坛根目录（与现有source文件夹合并）
3. 将 `discuz_plugin_content_audit.xml` 上传到论坛根目录

### 3. 安装插件

1. 登录Discuz管理后台
2. 进入 "应用" -> "插件"
3. 点击 "安装新插件"
4. 选择 `discuz_plugin_content_audit.xml` 文件
5. 点击 "导入" 完成安装

### 4. 配置插件

1. 安装完成后，在插件列表中找到 "内容审核插件"
2. 点击 "设置" 进入配置页面
3. 填写腾讯云配置信息：
   - **地域(Region)**: 例如 `ap-beijing`、`ap-shanghai` 等
   - **存储桶名称**: 您的COS存储桶名称，格式如 `mybucket-1250000000`
   - **SecretId**: 腾讯云API密钥ID
   - **SecretKey**: 腾讯云API密钥Key
   - **审核回调地址**: 可选，格式如 `https://yourdomain.com/source/plugin/content_audit/callback.php`

### 5. 测试功能

1. 配置完成后，点击 "执行内容审核"
2. 选择一个或多个主题进行测试
3. 点击 "开始审核" 提交任务
4. 在 "审核结果" 页面查看审核状态和结果

## 腾讯云配置说明

### 获取API密钥

1. 登录腾讯云控制台
2. 进入 "访问管理" -> "API密钥管理"
3. 创建或查看现有的API密钥
4. 记录SecretId和SecretKey

### 创建COS存储桶

1. 登录腾讯云控制台
2. 进入 "对象存储COS"
3. 创建存储桶，选择合适的地域
4. 记录存储桶名称（包含APPID后缀）

### 开通内容审核服务

1. 在腾讯云控制台搜索 "内容审核"
2. 开通文本内容审核服务
3. 确保账户有足够的余额或已购买相关套餐

## 目录结构

安装完成后的目录结构：

```
论坛根目录/
├── cos-sdk-v5-7.phar                    # 腾讯云COS SDK
├── discuz_plugin_content_audit.xml     # 插件配置文件
└── source/
    └── plugin/
        └── content_audit/               # 插件目录
            ├── admincp.inc.php          # 后台管理
            ├── cos_client.php           # COS客户端
            ├── install.php              # 安装脚本
            ├── callback.php             # 回调处理
            ├── audit_results.php        # 结果详情
            ├── language/
            │   └── lang_sc_utf8.php     # 语言包
            └── README.md                # 说明文档
```

## 常见问题

### Q: 安装时提示"文件不存在"
A: 请确保所有文件都已正确上传，特别是 `cos-sdk-v5-7.phar` 文件必须在论坛根目录。

### Q: 配置保存后提示连接失败
A: 请检查：
- SecretId和SecretKey是否正确
- 存储桶名称是否正确（包含APPID）
- 地域设置是否与存储桶地域一致
- 服务器网络是否正常

### Q: 审核任务提交后一直显示"审核中"
A: 这是正常现象，腾讯云异步审核需要一定时间。您可以：
- 等待几分钟后刷新页面查看
- 配置回调地址以便及时获取结果
- 查看腾讯云控制台的审核记录

### Q: 如何配置回调地址？
A: 回调地址格式为：`https://您的域名/source/plugin/content_audit/callback.php`
确保：
- 使用HTTPS协议
- 服务器可以接收外部POST请求
- 没有防火墙阻拦

## 卸载插件

如需卸载插件：

1. 在Discuz管理后台的插件列表中点击 "卸载"
2. 手动删除 `source/plugin/content_audit/` 目录
3. 删除 `cos-sdk-v5-7.phar` 文件（如果不再需要）
4. 删除 `discuz_plugin_content_audit.xml` 文件

注意：卸载插件会删除所有审核记录数据，请提前备份重要数据。

## 技术支持

如遇到问题，请：
1. 检查服务器错误日志
2. 查看插件日志文件（`source/plugin/content_audit/logs/`）
3. 确认腾讯云服务状态
4. 检查网络连接和防火墙设置
