<?php

/**
 * 内容审核插件 - 后台管理页面
 * 
 * <AUTHOR>
 * @version 1.0
 */

if(!defined('IN_DISCUZ') || !defined('IN_ADMINCP')) {
    exit('Access Denied');
}

require_once dirname(__FILE__) . '/cos_client.php';

// 获取操作类型
$operation = $_GET['operation'] ? $_GET['operation'] : $_POST['operation'];

// 处理表单提交
if($_POST['submit']) {
    if($operation == 'config') {
        // 保存配置
        $config = array(
            'cos_region' => $_POST['cos_region'],
            'cos_bucket' => $_POST['cos_bucket'],
            'cos_secret_id' => $_POST['cos_secret_id'],
            'cos_secret_key' => $_POST['cos_secret_key'],
            'audit_callback_url' => $_POST['audit_callback_url']
        );
        
        foreach($config as $key => $value) {
            $sql = "REPLACE INTO ".DB::table('common_setting')." (skey, svalue) VALUES ('$key', '".addslashes($value)."')";
            DB::query($sql);
        }
        
        cpmsg('配置保存成功！', 'action=plugins&operation=config&do='.$pluginid.'&identifier=content_audit&pmod=admincp', 'succeed');
    }
    
    if($operation == 'audit') {
        // 执行审核
        $thread_ids = $_POST['thread_ids'];
        if(empty($thread_ids)) {
            cpmsg('请选择要审核的主题！', '', 'error');
        }
        
        $cos_client = new ContentAuditCOSClient();
        $audit_results = array();
        
        foreach(explode(',', $thread_ids) as $tid) {
            $tid = intval($tid);
            if($tid > 0) {
                $result = $cos_client->auditThreadContent($tid);
                $audit_results[] = $result;
            }
        }
        
        cpmsg('审核任务已提交，共提交 '.count($audit_results).' 个任务！', 'action=plugins&operation=config&do='.$pluginid.'&identifier=content_audit&pmod=admincp&op=results', 'succeed');
    }
}

// 根据操作显示不同页面
$op = $_GET['op'] ? $_GET['op'] : $operation;
switch($op) {
    case 'config':
        showConfigPage();
        break;
    case 'audit':
        showAuditPage();
        break;
    case 'results':
        showResultsPage();
        break;
    case 'detail':
        include dirname(__FILE__) . '/audit_results.php';
        break;
    default:
        showMainPage();
        break;
}

/**
 * 显示主页面
 */
function showMainPage() {
    global $lang;
    
    echo '<div class="main">';
    echo '<div class="content">';
    echo '<h3>内容审核插件</h3>';
    echo '<div class="contentbox">';
    echo '<p>欢迎使用内容审核插件！本插件可以将论坛主题和回复内容上传到腾讯云COS进行异步审核。</p>';
    echo '<ul>';
    echo '<li><a href="?action=plugins&operation=config&do='.$_GET['do'].'&identifier=content_audit&pmod=admincp&op=config">配置腾讯云参数</a></li>';
    echo '<li><a href="?action=plugins&operation=config&do='.$_GET['do'].'&identifier=content_audit&pmod=admincp&op=audit">执行内容审核</a></li>';
    echo '<li><a href="?action=plugins&operation=config&do='.$_GET['do'].'&identifier=content_audit&pmod=admincp&op=results">查看审核结果</a></li>';
    echo '</ul>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
}

/**
 * 显示配置页面
 */
function showConfigPage() {
    global $lang;
    
    // 获取当前配置
    $config_keys = array('cos_region', 'cos_bucket', 'cos_secret_id', 'cos_secret_key', 'audit_callback_url');
    $config = array();
    
    $query = DB::query("SELECT skey, svalue FROM ".DB::table('common_setting')." WHERE skey IN ('".implode("','", $config_keys)."')");
    while($row = DB::fetch($query)) {
        $config[$row['skey']] = $row['svalue'];
    }
    
    echo '<div class="main">';
    echo '<div class="content">';
    echo '<h3>腾讯云配置</h3>';
    echo '<form method="post" action="">';
    echo '<input type="hidden" name="operation" value="config" />';
    echo '<table class="tb tb2">';
    echo '<tr><th class="partition" colspan="2">腾讯云COS配置</th></tr>';
    echo '<tr><td class="td1">地域(Region):</td><td class="td2"><input type="text" name="cos_region" value="'.htmlspecialchars($config['cos_region']).'" class="txt" placeholder="例如: ap-beijing" /></td></tr>';
    echo '<tr><td class="td1">存储桶名称:</td><td class="td2"><input type="text" name="cos_bucket" value="'.htmlspecialchars($config['cos_bucket']).'" class="txt" placeholder="例如: mybucket-1250000000" /></td></tr>';
    echo '<tr><td class="td1">SecretId:</td><td class="td2"><input type="text" name="cos_secret_id" value="'.htmlspecialchars($config['cos_secret_id']).'" class="txt" /></td></tr>';
    echo '<tr><td class="td1">SecretKey:</td><td class="td2"><input type="password" name="cos_secret_key" value="'.htmlspecialchars($config['cos_secret_key']).'" class="txt" /></td></tr>';
    echo '<tr><td class="td1">审核回调地址:</td><td class="td2"><input type="text" name="audit_callback_url" value="'.htmlspecialchars($config['audit_callback_url']).'" class="txt" placeholder="可选，审核完成后的回调地址" /></td></tr>';
    echo '</table>';
    echo '<div class="fixsel"><input type="submit" name="submit" value="保存配置" class="btn" /></div>';
    echo '</form>';
    echo '</div>';
    echo '</div>';
}

/**
 * 显示审核页面
 */
function showAuditPage() {
    global $lang;
    
    // 获取最新的主题列表
    $threads = array();
    $query = DB::query("SELECT t.tid, t.subject, t.author, t.dateline, f.name as forum_name 
                       FROM ".DB::table('forum_thread')." t 
                       LEFT JOIN ".DB::table('forum_forum')." f ON t.fid = f.fid 
                       WHERE t.displayorder >= 0 
                       ORDER BY t.dateline DESC 
                       LIMIT 50");
    
    while($row = DB::fetch($query)) {
        $threads[] = $row;
    }
    
    echo '<div class="main">';
    echo '<div class="content">';
    echo '<h3>执行内容审核</h3>';
    echo '<form method="post" action="">';
    echo '<input type="hidden" name="operation" value="audit" />';
    echo '<div class="contentbox">';
    echo '<p>选择要审核的主题（将审核主题内容及前10条回复）：</p>';
    echo '<table class="tb tb2">';
    echo '<tr><th width="30">选择</th><th>主题标题</th><th>作者</th><th>版块</th><th>发布时间</th></tr>';
    
    foreach($threads as $thread) {
        echo '<tr>';
        echo '<td><input type="checkbox" name="thread_check[]" value="'.$thread['tid'].'" /></td>';
        echo '<td>'.htmlspecialchars($thread['subject']).'</td>';
        echo '<td>'.htmlspecialchars($thread['author']).'</td>';
        echo '<td>'.htmlspecialchars($thread['forum_name']).'</td>';
        echo '<td>'.date('Y-m-d H:i:s', $thread['dateline']).'</td>';
        echo '</tr>';
    }
    
    echo '</table>';
    echo '<div class="fixsel">';
    echo '<input type="button" value="全选" onclick="checkAll()" class="btn" />';
    echo '<input type="button" value="反选" onclick="checkReverse()" class="btn" />';
    echo '<input type="submit" name="submit" value="开始审核" class="btn" onclick="return getSelectedThreads()" />';
    echo '</div>';
    echo '<input type="hidden" name="thread_ids" id="thread_ids" value="" />';
    echo '</div>';
    echo '</form>';
    echo '</div>';
    echo '</div>';
    
    // JavaScript代码
    echo '<script type="text/javascript">';
    echo 'function checkAll() {';
    echo '    var checkboxes = document.getElementsByName("thread_check[]");';
    echo '    for(var i = 0; i < checkboxes.length; i++) {';
    echo '        checkboxes[i].checked = true;';
    echo '    }';
    echo '}';
    echo 'function checkReverse() {';
    echo '    var checkboxes = document.getElementsByName("thread_check[]");';
    echo '    for(var i = 0; i < checkboxes.length; i++) {';
    echo '        checkboxes[i].checked = !checkboxes[i].checked;';
    echo '    }';
    echo '}';
    echo 'function getSelectedThreads() {';
    echo '    var checkboxes = document.getElementsByName("thread_check[]");';
    echo '    var selected = [];';
    echo '    for(var i = 0; i < checkboxes.length; i++) {';
    echo '        if(checkboxes[i].checked) {';
    echo '            selected.push(checkboxes[i].value);';
    echo '        }';
    echo '    }';
    echo '    if(selected.length == 0) {';
    echo '        alert("请选择要审核的主题！");';
    echo '        return false;';
    echo '    }';
    echo '    document.getElementById("thread_ids").value = selected.join(",");';
    echo '    return true;';
    echo '}';
    echo '</script>';
}

/**
 * 显示审核结果页面
 */
function showResultsPage() {
    global $lang;
    
    // 获取审核结果
    $results = array();
    $query = DB::query("SELECT * FROM ".DB::table('plugin_content_audit')." ORDER BY create_time DESC LIMIT 100");
    
    while($row = DB::fetch($query)) {
        $results[] = $row;
    }
    
    echo '<div class="main">';
    echo '<div class="content">';
    echo '<h3>审核结果</h3>';
    echo '<div class="contentbox">';
    
    if(empty($results)) {
        echo '<p>暂无审核结果</p>';
    } else {
        echo '<table class="tb tb2">';
        echo '<tr><th>主题ID</th><th>审核状态</th><th>审核结果</th><th>触发关键词</th><th>提交时间</th><th>操作</th></tr>';
        
        foreach($results as $result) {
            $audit_result = json_decode($result['audit_result'], true);
            $status_text = '';
            $result_text = '';
            $keywords = '';
            
            if($result['status'] == 'pending') {
                $status_text = '<span style="color: orange;">审核中</span>';
            } elseif($result['status'] == 'completed') {
                $status_text = '<span style="color: green;">已完成</span>';
                if($audit_result) {
                    $result_text = $audit_result['suggestion'] == 'Pass' ? '<span style="color: green;">通过</span>' : '<span style="color: red;">不通过</span>';
                    if(isset($audit_result['keywords'])) {
                        $keywords = implode(', ', $audit_result['keywords']);
                    }
                }
            } elseif($result['status'] == 'failed') {
                $status_text = '<span style="color: red;">失败</span>';
            }
            
            echo '<tr>';
            echo '<td>'.$result['thread_id'].'</td>';
            echo '<td>'.$status_text.'</td>';
            echo '<td>'.$result_text.'</td>';
            echo '<td>'.htmlspecialchars($keywords).'</td>';
            echo '<td>'.date('Y-m-d H:i:s', $result['create_time']).'</td>';
            echo '<td><a href="?action=plugins&operation=config&do='.$_GET['do'].'&identifier=content_audit&pmod=admincp&op=detail&id='.$result['id'].'">详情</a></td>';
            echo '</tr>';
        }
        
        echo '</table>';
    }
    
    echo '</div>';
    echo '</div>';
    echo '</div>';
}

?>
