<?php

/**
 * 内容审核插件 - 腾讯云审核回调处理
 * 
 * <AUTHOR>
 * @version 1.0
 */

// 定义常量以允许访问
define('IN_DISCUZ', true);
define('DISCUZ_ROOT', dirname(dirname(dirname(dirname(__FILE__)))) . '/');

// 引入Discuz核心文件
require_once DISCUZ_ROOT . './config/config_global.php';
require_once DISCUZ_ROOT . './config/config_ucenter.php';
require_once DISCUZ_ROOT . './source/class/class_core.php';

$discuz = C::app();
$discuz->init();

/**
 * 处理腾讯云审核回调
 */
function handleAuditCallback() {
    // 获取回调数据
    $input = file_get_contents('php://input');
    
    if(empty($input)) {
        http_response_code(400);
        echo json_encode(array('error' => 'No input data'));
        return;
    }
    
    // 记录回调日志
    $log_file = dirname(__FILE__) . '/logs/callback_' . date('Y-m-d') . '.log';
    $log_dir = dirname($log_file);
    if(!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    file_put_contents($log_file, date('Y-m-d H:i:s') . " - Callback received: " . $input . "\n", FILE_APPEND);
    
    try {
        // 解析回调数据
        $callback_data = json_decode($input, true);
        
        if(!$callback_data) {
            throw new Exception('Invalid JSON data');
        }
        
        // 验证回调数据格式
        if(!isset($callback_data['JobsDetail']) || !isset($callback_data['JobsDetail']['JobId'])) {
            throw new Exception('Invalid callback data format');
        }
        
        $job_detail = $callback_data['JobsDetail'];
        $job_id = $job_detail['JobId'];
        
        // 查找对应的审核记录
        $query = DB::query("SELECT * FROM ".DB::table('plugin_content_audit')." WHERE job_id = '".addslashes($job_id)."'");
        $audit_record = DB::fetch($query);
        
        if(!$audit_record) {
            throw new Exception('Audit record not found for job_id: ' . $job_id);
        }
        
        // 处理审核结果
        $audit_result = processAuditResult($job_detail);
        
        // 更新数据库记录
        $update_sql = "UPDATE ".DB::table('plugin_content_audit')." 
                      SET status = '".addslashes($audit_result['status'])."', 
                          audit_result = '".addslashes(json_encode($audit_result))."',
                          update_time = ".time()."
                      WHERE job_id = '".addslashes($job_id)."'";
        
        DB::query($update_sql);
        
        // 记录成功日志
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Callback processed successfully for job_id: " . $job_id . "\n", FILE_APPEND);
        
        // 返回成功响应
        http_response_code(200);
        echo json_encode(array('success' => true, 'message' => 'Callback processed successfully'));
        
    } catch(Exception $e) {
        // 记录错误日志
        file_put_contents($log_file, date('Y-m-d H:i:s') . " - Callback error: " . $e->getMessage() . "\n", FILE_APPEND);
        
        // 返回错误响应
        http_response_code(500);
        echo json_encode(array('error' => $e->getMessage()));
    }
}

/**
 * 处理审核结果
 * 
 * @param array $job_detail 审核任务详情
 * @return array 处理后的审核结果
 */
function processAuditResult($job_detail) {
    $result = array();
    
    // 基本状态信息
    if($job_detail['State'] == 'Success') {
        $result['status'] = 'completed';
        $result['suggestion'] = isset($job_detail['Suggestion']) ? $job_detail['Suggestion'] : '';
        $result['label'] = isset($job_detail['Label']) ? $job_detail['Label'] : '';
        $result['result'] = isset($job_detail['Result']) ? $job_detail['Result'] : '';
        
        // 提取关键词
        $result['keywords'] = extractKeywordsFromJobDetail($job_detail);
        
        // 提取详细的审核信息
        $result['details'] = extractAuditDetails($job_detail);
        
    } elseif($job_detail['State'] == 'Failed') {
        $result['status'] = 'failed';
        $result['error'] = isset($job_detail['Message']) ? $job_detail['Message'] : 'Unknown error';
    } else {
        $result['status'] = 'pending';
    }
    
    // 保存原始数据
    $result['raw_data'] = $job_detail;
    
    return $result;
}

/**
 * 从审核详情中提取关键词
 * 
 * @param array $job_detail 审核任务详情
 * @return array 关键词列表
 */
function extractKeywordsFromJobDetail($job_detail) {
    $keywords = array();
    
    if(isset($job_detail['SectionCount']) && $job_detail['SectionCount'] > 0) {
        $sections = isset($job_detail['Section']) ? $job_detail['Section'] : array();
        
        foreach($sections as $section) {
            // 涉黄关键词
            if(isset($section['PornInfo']['Keywords']) && !empty($section['PornInfo']['Keywords'])) {
                $keywords = array_merge($keywords, explode(',', $section['PornInfo']['Keywords']));
            }
            
            // 涉恐关键词
            if(isset($section['TerrorismInfo']['Keywords']) && !empty($section['TerrorismInfo']['Keywords'])) {
                $keywords = array_merge($keywords, explode(',', $section['TerrorismInfo']['Keywords']));
            }
            
            // 涉政关键词
            if(isset($section['PoliticsInfo']['Keywords']) && !empty($section['PoliticsInfo']['Keywords'])) {
                $keywords = array_merge($keywords, explode(',', $section['PoliticsInfo']['Keywords']));
            }
            
            // 广告关键词
            if(isset($section['AdsInfo']['Keywords']) && !empty($section['AdsInfo']['Keywords'])) {
                $keywords = array_merge($keywords, explode(',', $section['AdsInfo']['Keywords']));
            }
            
            // 违法关键词
            if(isset($section['IllegalInfo']['Keywords']) && !empty($section['IllegalInfo']['Keywords'])) {
                $keywords = array_merge($keywords, explode(',', $section['IllegalInfo']['Keywords']));
            }
            
            // 谩骂关键词
            if(isset($section['AbuseInfo']['Keywords']) && !empty($section['AbuseInfo']['Keywords'])) {
                $keywords = array_merge($keywords, explode(',', $section['AbuseInfo']['Keywords']));
            }
        }
    }
    
    return array_unique(array_filter($keywords));
}

/**
 * 提取详细的审核信息
 * 
 * @param array $job_detail 审核任务详情
 * @return array 详细审核信息
 */
function extractAuditDetails($job_detail) {
    $details = array();
    
    if(isset($job_detail['SectionCount']) && $job_detail['SectionCount'] > 0) {
        $sections = isset($job_detail['Section']) ? $job_detail['Section'] : array();
        
        foreach($sections as $index => $section) {
            $section_detail = array(
                'section_index' => $index,
                'start_byte' => isset($section['StartByte']) ? $section['StartByte'] : 0,
                'porn_info' => isset($section['PornInfo']) ? $section['PornInfo'] : null,
                'terrorism_info' => isset($section['TerrorismInfo']) ? $section['TerrorismInfo'] : null,
                'politics_info' => isset($section['PoliticsInfo']) ? $section['PoliticsInfo'] : null,
                'ads_info' => isset($section['AdsInfo']) ? $section['AdsInfo'] : null,
                'illegal_info' => isset($section['IllegalInfo']) ? $section['IllegalInfo'] : null,
                'abuse_info' => isset($section['AbuseInfo']) ? $section['AbuseInfo'] : null,
            );
            
            $details[] = $section_detail;
        }
    }
    
    return $details;
}

// 处理回调请求
if($_SERVER['REQUEST_METHOD'] == 'POST') {
    handleAuditCallback();
} else {
    // GET请求返回简单的状态信息
    http_response_code(200);
    echo json_encode(array(
        'status' => 'ok',
        'message' => 'Content audit callback endpoint',
        'timestamp' => time()
    ));
}

?>
