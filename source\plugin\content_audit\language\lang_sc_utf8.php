<?php

/**
 * 内容审核插件 - 简体中文语言包
 * 
 * <AUTHOR>
 * @version 1.0
 */

if(!defined('IN_DISCUZ')) {
    exit('Access Denied');
}

$lang = array(
    'content_audit' => '内容审核',
    'content_audit_desc' => '腾讯云内容审核插件，支持将论坛主题和回复内容上传至腾讯云COS进行异步审核',
    
    // 菜单项
    'menu_config' => '配置管理',
    'menu_audit' => '执行审核',
    'menu_results' => '审核结果',
    
    // 配置页面
    'config_title' => '腾讯云配置',
    'config_cos_region' => '地域(Region)',
    'config_cos_region_desc' => '腾讯云COS存储桶所在地域，例如: ap-beijing',
    'config_cos_bucket' => '存储桶名称',
    'config_cos_bucket_desc' => '腾讯云COS存储桶名称，例如: mybucket-1250000000',
    'config_cos_secret_id' => 'SecretId',
    'config_cos_secret_id_desc' => '腾讯云API密钥SecretId',
    'config_cos_secret_key' => 'SecretKey',
    'config_cos_secret_key_desc' => '腾讯云API密钥SecretKey',
    'config_audit_callback_url' => '审核回调地址',
    'config_audit_callback_url_desc' => '可选，审核完成后的回调地址',
    'config_save' => '保存配置',
    'config_save_success' => '配置保存成功！',
    
    // 审核页面
    'audit_title' => '执行内容审核',
    'audit_desc' => '选择要审核的主题（将审核主题内容及前10条回复）',
    'audit_select_all' => '全选',
    'audit_select_reverse' => '反选',
    'audit_start' => '开始审核',
    'audit_no_selection' => '请选择要审核的主题！',
    'audit_submit_success' => '审核任务已提交',
    
    // 结果页面
    'results_title' => '审核结果',
    'results_no_data' => '暂无审核结果',
    'results_thread_id' => '主题ID',
    'results_status' => '审核状态',
    'results_result' => '审核结果',
    'results_keywords' => '触发关键词',
    'results_create_time' => '提交时间',
    'results_operation' => '操作',
    'results_detail' => '详情',
    
    // 状态文本
    'status_pending' => '审核中',
    'status_completed' => '已完成',
    'status_failed' => '失败',
    'result_pass' => '通过',
    'result_block' => '不通过',
    
    // 表格标题
    'table_select' => '选择',
    'table_subject' => '主题标题',
    'table_author' => '作者',
    'table_forum' => '版块',
    'table_dateline' => '发布时间',
    
    // 错误信息
    'error_config_incomplete' => '腾讯云COS配置不完整，请先配置相关参数',
    'error_thread_not_found' => '主题不存在',
    'error_audit_failed' => '审核提交失败',
    'error_no_permission' => '没有权限执行此操作',
    
    // 成功信息
    'success_audit_submitted' => '审核任务已成功提交',
    'success_config_saved' => '配置已成功保存',
    
    // 其他
    'plugin_name' => '内容审核插件',
    'plugin_version' => '版本 1.0',
    'plugin_author' => '内容审核系统',
    'welcome_message' => '欢迎使用内容审核插件！本插件可以将论坛主题和回复内容上传到腾讯云COS进行异步审核。',
    
    // 帮助信息
    'help_config' => '请在腾讯云控制台获取相关配置信息',
    'help_audit' => '选择要审核的主题，系统将自动获取主题内容和前10条回复进行审核',
    'help_results' => '查看已提交的审核任务结果和状态',
    
    // 审核类型
    'audit_type_porn' => '涉黄',
    'audit_type_terrorism' => '涉恐',
    'audit_type_politics' => '涉政',
    'audit_type_ads' => '广告',
    'audit_type_illegal' => '违法',
    'audit_type_abuse' => '谩骂',
    
    // 建议类型
    'suggestion_pass' => '建议通过',
    'suggestion_review' => '建议人工复审',
    'suggestion_block' => '建议拦截',
);

?>
